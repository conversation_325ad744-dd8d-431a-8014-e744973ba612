"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/data.ts":
/*!*********************!*\
  !*** ./lib/data.ts ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   certificates: () => (/* binding */ certificates),\n/* harmony export */   contactInfo: () => (/* binding */ contactInfo),\n/* harmony export */   education: () => (/* binding */ education),\n/* harmony export */   footerInfo: () => (/* binding */ footerInfo),\n/* harmony export */   heroData: () => (/* binding */ heroData),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   workExperience: () => (/* binding */ workExperience)\n/* harmony export */ });\n// Data & Asset Manifest - Single source of truth for all portfolio content\n// This file contains all the structured data used throughout the portfolio application\n// Type definitions for data structures\n// Hero Section Data (HERO-01 to HERO-09)\nconst heroData = {\n    name: \"Janmejay Tiwari\",\n    bio: \"A skilled and passionate developer with experience in creating modern, responsive, and scalable web applications using React, Next.js, and AI/ML technologies.\",\n    profileImage: \"/assets/images/Profile.jpg\",\n    buttons: {\n        viewWork: \"View Work\",\n        viewResume: \"View Resume\"\n    },\n    resumeLink: \"/assets/docs/Resume_Janmejay_Tiwari\",\n    socialLinks: {\n        github: \"https://github.com/Janmejay3108\",\n        linkedin: \"https://linkedin.com/in/janmejay-tiwari\",\n        twitter: \"https://twitter.com/placeholder_handle\"\n    }\n};\n// Work Experience Data (EXP-01 to EXP-11)\nconst workExperience = [\n    {\n        id: \"exp-01\",\n        companyName: \"Celebal Technologies\",\n        companyLogo: \"/assets/images/logo_celebal.svg\",\n        role: \"React Developer Intern\",\n        timeline: \"May 2025 - July 2025\",\n        details: [\n            \"Worked on the development of a real-time chat application for Bank of Baroda, enhancing customer support and engagement.\",\n            \"Built responsive, component-based UIs using React.js, JavaScript, and Tailwind CSS.\"\n        ],\n        certificateLink: \"#\"\n    },\n    {\n        id: \"exp-02\",\n        companyName: \"1stop.ai\",\n        companyLogo: \"/assets/images/logo_1stopai.svg\",\n        role: \"Frontend developer Intern\",\n        timeline: \"Timeline not specified\",\n        details: [\n            \"Collaborated with a team to develop responsive web application using MERN stack.\",\n            \"Implemented frontend components using React.js.\",\n            \"Assisted in database design and implementation with Firebase.\",\n            \"Integrated AWS S3 for media storage and content delivery.\"\n        ],\n        certificateLink: \"#\"\n    }\n];\n// Project Work Data (PROJ-01 to PROJ-10)\nconst projects = [\n    {\n        id: \"proj-01\",\n        name: \"Web accessibility analyser\",\n        purpose: \"A platform that analyses websites for accessibility issues and provides AI-powered remediation suggestions.\",\n        techStack: \"\",\n        learnings: \"Learned to integrate third-party accessibility testing engines and leverage AI for providing actionable feedback to developers.\",\n        githubLink: \"https://github.com/Janmejay3108\"\n    },\n    {\n        id: \"proj-02\",\n        name: \"Secure File Sharing Platform\",\n        purpose: \"A full-stack platform for transferring files up to 100MB with single-use codes, i18n support, and drag-and-drop UI.\",\n        techStack: \"\",\n        learnings: \"Gained experience in building end-to-end applications with secure file handling, internationalization, and containerization with Docker.\",\n        githubLink: \"https://github.com/Janmejay3108\"\n    }\n];\n// Certificates Data (CERT-01 to CERT-18)\nconst certificates = [\n    {\n        id: \"cert-01\",\n        name: \"AWS Cloud Solutions Architect\",\n        issuerName: \"Amazon Web Services\",\n        issuerLogo: \"/assets/images/logo_aws.svg\"\n    },\n    {\n        id: \"cert-02\",\n        name: \"Full Stack Developer\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-03\",\n        name: \"Database and SQL for data science with Python\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-04\",\n        name: \"RAG and Agentic AI\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-05\",\n        name: \"Artificial Intelligence Essentials\",\n        issuerName: \"Google\",\n        issuerLogo: \"/assets/images/logo_google.svg\"\n    },\n    {\n        id: \"cert-06\",\n        name: \"Convolutional Neural Networks\",\n        issuerName: \"Deeplearning.AI\",\n        issuerLogo: \"/assets/images/logo_deeplearning_ai.svg\"\n    }\n];\n// Education Data (EDU-01 to EDU-04)\nconst education = {\n    degree: \"BTech in Electronics and Communication Engineering\",\n    institution: \"Institute of Engineering and Management, Kolkata\",\n    timeline: \"2022-2026\",\n    detail: \"CGPA: 9.38\"\n};\n// Contact Information (CONTACT-01 to CONTACT-02)\nconst contactInfo = {\n    email: \"<EMAIL>\",\n    phone: \"+91 9163083482\"\n};\n// Footer Information (FOOTER-01)\nconst footerInfo = {\n    name: \"Janmejay Tiwari\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/data.ts\n"));

/***/ })

});