"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/data.ts":
/*!*********************!*\
  !*** ./lib/data.ts ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   certificates: () => (/* binding */ certificates),\n/* harmony export */   contactInfo: () => (/* binding */ contactInfo),\n/* harmony export */   education: () => (/* binding */ education),\n/* harmony export */   footerInfo: () => (/* binding */ footerInfo),\n/* harmony export */   heroData: () => (/* binding */ heroData),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   workExperience: () => (/* binding */ workExperience)\n/* harmony export */ });\n// Data & Asset Manifest - Single source of truth for all portfolio content\n// This file contains all the structured data used throughout the portfolio application\n// Type definitions for data structures\n// Hero Section Data (HERO-01 to HERO-09)\nconst heroData = {\n    name: \"Janmejay Tiwari\",\n    bio: \"A skilled and passionate developer with experience in creating modern, responsive, and scalable web applications using React, Next.js, and AI/ML technologies.\",\n    profileImage: \"/assets/images/Profile.jpg\",\n    buttons: {\n        viewWork: \"View Work\",\n        viewResume: \"View Resume\"\n    },\n    resumeLink: \"/assets/docs/Resume_Janmejay_Tiwari\",\n    socialLinks: {\n        github: \"https://github.com/Janmejay3108\",\n        linkedin: \"https://linkedin.com/in/janmejay-tiwari\",\n        twitter: \"https://x.com/Janmeja_y\"\n    }\n};\n// Work Experience Data (EXP-01 to EXP-11)\nconst workExperience = [\n    {\n        id: \"exp-01\",\n        companyName: \"Celebal Technologies\",\n        companyLogo: \"/assets/images/logo_celebal.svg\",\n        role: \"React Developer Intern\",\n        timeline: \"May 2025 - July 2025\",\n        details: [\n            \"Worked on the development of a real-time chat application for Bank of Baroda, enhancing customer support and engagement.\",\n            \"Built responsive, component-based UIs using React.js, JavaScript, and Tailwind CSS.\"\n        ],\n        certificateLink: \"https://drive.google.com/file/d/15YBIy42FxAL-MBu8AApibsSMeBCZ208e/view?usp=sharing\"\n    },\n    {\n        id: \"exp-02\",\n        companyName: \"1stop.ai\",\n        companyLogo: \"/assets/images/logo_1stopai.svg\",\n        role: \"Frontend developer Intern\",\n        timeline: \"March 2024 - April 2024.\",\n        details: [\n            \"Collaborated with a team to develop responsive web application using MERN stack.\",\n            \"Implemented frontend components using React.js.\",\n            \"Assisted in database design and implementation with Firebase.\",\n            \"Integrated AWS S3 for media storage and content delivery.\"\n        ],\n        certificateLink: \"https://drive.google.com/file/d/1Hi-eA_mT2DMZPnIUgGvEWqbPAtC50tKs/view?usp=sharing\"\n    }\n];\n// Project Work Data (PROJ-01 to PROJ-10)\nconst projects = [\n    {\n        id: \"proj-01\",\n        name: \"Web accessibility analyser\",\n        purpose: \"A platform that analyses websites for accessibility issues and provides AI-powered remediation suggestions.\",\n        techStack: \"\",\n        learnings: \"Learned to integrate third-party accessibility testing engines and leverage AI for providing actionable feedback to developers.\",\n        githubLink: \"https://github.com/Janmejay3108/Accessibility-analyzer\"\n    },\n    {\n        id: \"proj-02\",\n        name: \"Secure File Sharing Platform\",\n        purpose: \"A full-stack platform for transferring files up to 100MB with single-use codes, i18n support, and drag-and-drop UI.\",\n        techStack: \"\",\n        learnings: \"Gained experience in building end-to-end applications with secure file handling, internationalization, and containerization with Docker.\",\n        githubLink: \"https://github.com/Janmejay3108/File_Transfer_application\"\n    }\n];\n// Certificates Data (CERT-01 to CERT-18)\nconst certificates = [\n    {\n        id: \"cert-01\",\n        name: \"AWS Cloud Solutions Architect\",\n        issuerName: \"Amazon Web Services\",\n        issuerLogo: \"/assets/images/logo_aws.svg\"\n    },\n    {\n        id: \"cert-02\",\n        name: \"Full Stack Developer\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-03\",\n        name: \"Database and SQL for data science with Python\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-04\",\n        name: \"RAG and Agentic AI\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-05\",\n        name: \"Artificial Intelligence Essentials\",\n        issuerName: \"Google\",\n        issuerLogo: \"/assets/images/logo_google.svg\"\n    },\n    {\n        id: \"cert-06\",\n        name: \"Convolutional Neural Networks\",\n        issuerName: \"Deeplearning.AI\",\n        issuerLogo: \"/assets/images/logo_deeplearning_ai.svg\"\n    }\n];\n// Education Data (EDU-01 to EDU-04)\nconst education = {\n    degree: \"BTech in Electronics and Communication Engineering\",\n    institution: \"Institute of Engineering and Management, Kolkata\",\n    timeline: \"2022-2026\",\n    detail: \"CGPA: 9.38\"\n};\n// Contact Information (CONTACT-01 to CONTACT-02)\nconst contactInfo = {\n    email: \"<EMAIL>\",\n    phone: \"+91 9163083482\"\n};\n// Footer Information (FOOTER-01)\nconst footerInfo = {\n    name: \"Janmejay Tiwari\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/data.ts\n"));

/***/ })

});