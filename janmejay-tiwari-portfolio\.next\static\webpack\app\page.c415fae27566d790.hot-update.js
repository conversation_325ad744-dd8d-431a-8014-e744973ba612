/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./lib/data.ts\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-4 text-accent\",\n                    children: \"Portfolio Setup Complete!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"bg-card p-6 rounded-lg mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                            children: \"Button Component Test\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"primary\",\n                                    onClick: ()=>alert('Primary button clicked!'),\n                                    children: \"Primary Button\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"outline\",\n                                    onClick: ()=>alert('Outline button clicked!'),\n                                    children: \"Outline Button\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"primary\",\n                                    href: \"#work-experience\",\n                                    children: \"Link Button\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"outline\",\n                                    disabled: true,\n                                    children: \"Disabled Button\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"bg-card p-6 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                    children: \"Hero Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-textSecondary mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Name:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 52\n                                        }, this),\n                                        \" \",\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.heroData.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-textSecondary mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Bio:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 52\n                                        }, this),\n                                        \" \",\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.heroData.bio\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-textSecondary mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"GitHub:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 52\n                                        }, this),\n                                        \" \",\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.heroData.socialLinks.github\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"bg-card p-6 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                    children: \"Work Experience\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.workExperience.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 p-4 bg-background rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-accent\",\n                                                children: job.companyName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-textSecondary\",\n                                                children: [\n                                                    job.role,\n                                                    \" | \",\n                                                    job.timeline\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, job.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"bg-card p-6 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                    children: [\n                                        \"Projects (\",\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.projects.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 p-4 bg-background rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-accent\",\n                                                children: project.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-textSecondary mb-2\",\n                                                children: project.purpose\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-textSecondary mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Learnings:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 56\n                                                    }, this),\n                                                    \" \",\n                                                    project.learnings\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-textSecondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"GitHub:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 51\n                                                    }, this),\n                                                    \" \",\n                                                    project.githubLink\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, project.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"bg-card p-6 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                    children: [\n                                        \"Certificates (\",\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.certificates.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: _lib_data__WEBPACK_IMPORTED_MODULE_1__.certificates.map((cert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-background rounded\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-accent\",\n                                                    children: cert.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-textSecondary\",\n                                                    children: [\n                                                        \"Issued by: \",\n                                                        cert.issuerName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, cert.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"bg-card p-6 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                    children: \"Education\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-textSecondary mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Degree:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 52\n                                        }, this),\n                                        \" \",\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.education.degree\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-textSecondary mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Institution:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 52\n                                        }, this),\n                                        \" \",\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.education.institution\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-textSecondary mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Timeline:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 52\n                                        }, this),\n                                        \" \",\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.education.timeline\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-textSecondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"CGPA:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 47\n                                        }, this),\n                                        \" \",\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.education.detail\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/data.ts":
/*!*********************!*\
  !*** ./lib/data.ts ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   certificates: () => (/* binding */ certificates),\n/* harmony export */   contactInfo: () => (/* binding */ contactInfo),\n/* harmony export */   education: () => (/* binding */ education),\n/* harmony export */   footerInfo: () => (/* binding */ footerInfo),\n/* harmony export */   heroData: () => (/* binding */ heroData),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   workExperience: () => (/* binding */ workExperience)\n/* harmony export */ });\n// Data & Asset Manifest - Single source of truth for all portfolio content\n// This file contains all the structured data used throughout the portfolio application\n// Type definitions for data structures\n// Hero Section Data (HERO-01 to HERO-09)\nconst heroData = {\n    name: \"Janmejay Tiwari\",\n    bio: \"A skilled and passionate developer with experience in creating modern, responsive, and scalable web applications using React, Next.js, and AI/ML technologies.\",\n    profileImage: \"/assets/images/janmejay_tiwari_profile.jpg\",\n    buttons: {\n        viewWork: \"View Work\",\n        viewResume: \"View Resume\"\n    },\n    resumeLink: \"/assets/docs/Resume_Janmejay_Tiwari.pdf\",\n    socialLinks: {\n        github: \"https://github.com/Janmejay3108\",\n        linkedin: \"https://linkedin.com/in/janmejay-tiwari\",\n        twitter: \"https://twitter.com/placeholder_handle\"\n    }\n};\n// Work Experience Data (EXP-01 to EXP-11)\nconst workExperience = [\n    {\n        id: \"exp-01\",\n        companyName: \"Celebal Technologies\",\n        companyLogo: \"/assets/images/logo_celebal.svg\",\n        role: \"React Developer Intern\",\n        timeline: \"May 2025 - July 2025\",\n        details: [\n            \"Worked on the development of a real-time chat application for Bank of Baroda, enhancing customer support and engagement.\",\n            \"Built responsive, component-based UIs using React.js, JavaScript, and Tailwind CSS.\"\n        ],\n        certificateLink: \"#\"\n    },\n    {\n        id: \"exp-02\",\n        companyName: \"1stop.ai\",\n        companyLogo: \"/assets/images/logo_1stopai.svg\",\n        role: \"Frontend developer Intern\",\n        timeline: \"Timeline not specified\",\n        details: [\n            \"Collaborated with a team to develop responsive web application using MERN stack.\",\n            \"Implemented frontend components using React.js.\",\n            \"Assisted in database design and implementation with Firebase.\",\n            \"Integrated AWS S3 for media storage and content delivery.\"\n        ],\n        certificateLink: \"#\"\n    }\n];\n// Project Work Data (PROJ-01 to PROJ-10)\nconst projects = [\n    {\n        id: \"proj-01\",\n        name: \"Web accessibility analyser\",\n        purpose: \"A platform that analyses websites for accessibility issues and provides AI-powered remediation suggestions.\",\n        techStack: \"\",\n        learnings: \"Learned to integrate third-party accessibility testing engines and leverage AI for providing actionable feedback to developers.\",\n        githubLink: \"https://github.com/Janmejay3108\"\n    },\n    {\n        id: \"proj-02\",\n        name: \"Secure File Sharing Platform\",\n        purpose: \"A full-stack platform for transferring files up to 100MB with single-use codes, i18n support, and drag-and-drop UI.\",\n        techStack: \"\",\n        learnings: \"Gained experience in building end-to-end applications with secure file handling, internationalization, and containerization with Docker.\",\n        githubLink: \"https://github.com/Janmejay3108\"\n    }\n];\n// Certificates Data (CERT-01 to CERT-18)\nconst certificates = [\n    {\n        id: \"cert-01\",\n        name: \"AWS Cloud Solutions Architect\",\n        issuerName: \"Amazon Web Services\",\n        issuerLogo: \"/assets/images/logo_aws.svg\"\n    },\n    {\n        id: \"cert-02\",\n        name: \"Full Stack Developer\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-03\",\n        name: \"Database and SQL for data science with Python\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-04\",\n        name: \"RAG and Agentic AI\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-05\",\n        name: \"Artificial Intelligence Essentials\",\n        issuerName: \"Google\",\n        issuerLogo: \"/assets/images/logo_google.svg\"\n    },\n    {\n        id: \"cert-06\",\n        name: \"Convolutional Neural Networks\",\n        issuerName: \"Deeplearning.AI\",\n        issuerLogo: \"/assets/images/logo_deeplearning_ai.svg\"\n    }\n];\n// Education Data (EDU-01 to EDU-04)\nconst education = {\n    degree: \"BTech in Electronics and Communication Engineering\",\n    institution: \"Institute of Engineering and Management, Kolkata\",\n    timeline: \"2022-2026\",\n    detail: \"CGPA: 9.38\"\n};\n// Contact Information (CONTACT-01 to CONTACT-02)\nconst contactInfo = {\n    email: \"<EMAIL>\",\n    phone: \"+91 9163083482\"\n};\n// Footer Information (FOOTER-01)\nconst footerInfo = {\n    name: \"Janmejay Tiwari\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/data.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(app-pages-browser)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDb2tydHclNUMlNUNEZXNrdG9wJTVDJTVDUG9ydGZvbGlvJTVDJTVDamFubWVqYXktdGl3YXJpLXBvcnRmb2xpbyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0pBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxva3J0d1xcXFxEZXNrdG9wXFxcXFBvcnRmb2xpb1xcXFxqYW5tZWpheS10aXdhcmktcG9ydGZvbGlvXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ })

});